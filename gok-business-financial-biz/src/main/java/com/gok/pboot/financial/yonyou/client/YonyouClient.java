package com.gok.pboot.financial.yonyou.client;

import com.dtflys.forest.annotation.*;
import com.gok.pboot.financial.yonyou.dto.DeptSaveRequest;
import com.gok.pboot.financial.yonyou.dto.DeptSaveResponse;
import com.gok.pboot.financial.yonyou.dto.EpmOrgTreeResponse;
import com.gok.pboot.financial.yonyou.model.AccessTokenResponse;
import com.gok.pboot.financial.yonyou.model.RefreshTokenResponse;
import com.gok.pboot.financial.yonyou.model.UserTokenResponse;
import com.gok.pboot.financial.yonyou.model.YonyouResult;

import java.util.List;
import java.util.Map;

/**
 * 用友客户端
 *
 * <AUTHOR>
 * @date 2025/09/24
 */
@BaseRequest(baseURL = "${yonyouBaseUrl}")
public interface YonyouClient {

    /**
     * 获取访问令牌
     *
     * @param params 参数
     * @return {@link YonyouResult }<{@link AccessTokenResponse }>
     */
    @Get(url = "/iuap-api-auth/open-auth/selfAppAuth/getAccessToken", contentType = "application/x-www-form-urlencoded")
    YonyouResult<AccessTokenResponse> getAccessToken(@Query Map<String, String> params);

    /**
     * 获取访问令牌V2（多数据中心适配版本）
     *
     * @param tokenUrl 动态token地址
     * @param params   参数
     * @return {@link YonyouResult }<{@link AccessTokenResponse }>
     */
    @Get(url = "${tokenUrl}/open-auth/selfAppAuth/getAccessToken", contentType = "application/x-www-form-urlencoded")
    YonyouResult<AccessTokenResponse> getAccessTokenV2(@Var("tokenUrl") String tokenUrl, @Query Map<String, String> params);

    /**
     * 通过appkey获取租户id
     *
     * @param params 参数
     * @return {@link YonyouResult }<{@link String }>
     */
    @Get(url = "/iuap-api-auth/open-auth/selfAppAuth/getTenantId", contentType = "application/x-www-form-urlencoded")
    YonyouResult<String> getTenantId(@Query Map<String, String> params);

    /**
     * 通过accessToken和友空间免登code获取userToken
     *
     * @param params 参数
     * @return {@link YonyouResult }<{@link UserTokenResponse }>
     */
    @Get(url = "/iuap-api-auth/open-auth/selfAppAuth/getUserToken", contentType = "application/x-www-form-urlencoded")
    YonyouResult<UserTokenResponse> getUserToken(@Query Map<String, String> params);

    /**
     * 使用refreshToken刷新userToken
     *
     * @param params 参数
     * @return {@link YonyouResult }<{@link RefreshTokenResponse }>
     */
    @Get(url = "/iuap-api-auth/open-auth/userToken/refresh", contentType = "application/x-www-form-urlencoded")
    YonyouResult<RefreshTokenResponse> refreshUserToken(@Query Map<String, String> params);

    /**
     * 查询企业绩效组织体系树
     *
     * @param accessToken 访问令牌
     * @return {@link YonyouResult }<{@link List }<{@link EpmOrgTreeResponse }>>
     */
    @Post(url = "/iuap-api-gateway/yonbip/digitalModel/epmorg/querytree", contentType = "application/x-www-form-urlencoded")
    YonyouResult<List<EpmOrgTreeResponse>> queryEpmOrgTree(@Query("access_token") String accessToken);

    /**
     * 部门保存
     * 用于部门新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
     *
     * @param accessToken 访问令牌
     * @param deptSaveRequest 部门保存请求参数
     * @return {@link YonyouResult }<{@link DeptSaveResponse }>
     */
    @Post(url = "/yonbip/digitalModel/admindept/save", contentType = "application/json")
    YonyouResult<DeptSaveResponse> saveDept(@Query("access_token") String accessToken, @Body DeptSaveRequest deptSaveRequest);
}
