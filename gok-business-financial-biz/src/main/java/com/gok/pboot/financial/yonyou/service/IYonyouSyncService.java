/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * EPM组织服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.service;

import com.gok.pboot.financial.yonyou.dto.DeptSaveRequest;
import com.gok.pboot.financial.yonyou.dto.DeptSaveResponse;
import com.gok.pboot.financial.yonyou.dto.EpmOrgTreeResponse;

import java.util.List;

/**
 * EPM组织服务接口
 * 提供用友开放平台EPM组织相关的业务服务
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IYonyouSyncService {

    /**
     * 查询 EPM 组织树
     *
     * @return {@link List }<{@link EpmOrgTreeResponse }>
     */
    List<EpmOrgTreeResponse> queryEpmOrgTree();



    /**
     * 将本地部门同步至用友
     * 遍历本地部门列表，将每个部门保存到用友系统中
     *
     * @return {@link List}<{@link DeptSaveResponse}> 部门同步结果列表
     */
    List<DeptSaveResponse> syncLocalDeptsToYonyou();
}