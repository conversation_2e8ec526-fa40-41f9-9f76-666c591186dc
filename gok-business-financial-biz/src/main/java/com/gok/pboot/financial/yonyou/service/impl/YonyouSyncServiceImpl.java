/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * EPM组织服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.gok.bcp.upms.common.UpmsConstants;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.util.FinancePropertiesUtils;
import com.gok.pboot.financial.yonyou.client.YonyouClient;
import com.gok.pboot.financial.yonyou.dto.DeptSaveRequest;
import com.gok.pboot.financial.yonyou.dto.DeptSaveResponse;
import com.gok.pboot.financial.yonyou.dto.EpmOrgTreeResponse;
import com.gok.pboot.financial.yonyou.model.YonyouResult;
import com.gok.pboot.financial.yonyou.service.IYonyouAuthService;
import com.gok.pboot.financial.yonyou.service.IYonyouSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EPM组织服务实现类
 * 提供用友开放平台EPM组织相关的业务服务实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class YonyouSyncServiceImpl implements IYonyouSyncService {

    @Resource
    private YonyouClient yonyouClient;
    
    @Resource
    private IYonyouAuthService yonyouAuthService;

    @Resource
    private RemoteOutMultiDeptService remoteOutMultiDeptService;

    /**
     * 查询企业绩效组织体系树
     * 获取用友开放平台企业绩效管理模块的组织体系树结构
     *
     * @return {@link List }<{@link EpmOrgTreeResponse }> 企业绩效组织体系树列表
     */
    @Override
    public List<EpmOrgTreeResponse> queryEpmOrgTree() {
        // 使用YonyouClient请求
        YonyouResult<List<EpmOrgTreeResponse>> response = yonyouClient.queryEpmOrgTree(yonyouAuthService.getAccessToken());
        return response.getResult();
    }

    /**
     * 部门保存
     * 用于部门新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
     *
     * @param deptSaveRequest 部门保存请求参数
     * @return {@link DeptSaveResponse} 部门保存响应
     */
    public DeptSaveResponse saveDept(DeptSaveRequest deptSaveRequest) {

        // 获取访问令牌
        String accessToken = yonyouAuthService.getAccessToken();

        // 调用用友开放平台部门保存接口
        YonyouResult<DeptSaveResponse> response = yonyouClient.saveDept(accessToken, deptSaveRequest);

        return response.getResult();

    }

    /**
     * 查询本地部门列表
     * 通过中台服务获取本地部门信息
     *
     * @return {@link List}<{@link Object}> 本地部门列表
     */
    public List<MultiDimensionDeptDto> getLocalDeptList() {

        // 调用中台服务获取部门列表
        List<MultiDimensionDeptDto> multiDimensionDeptDtoList = remoteOutMultiDeptService.getDeptList(FinancePropertiesUtils.DEPT_CAT_NAME, null, null)
                .getData().stream()
                .filter(dept -> UpmsConstants.STATUS_NORMAL.equals(dept.getStatus()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(multiDimensionDeptDtoList)) {
            return multiDimensionDeptDtoList;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 将本地部门同步至用友
     * 遍历本地部门列表，将每个部门保存到用友系统中
     *
     * @return {@link List}<{@link DeptSaveResponse}> 部门同步结果列表
     */
    @Override
    public List<DeptSaveResponse> syncLocalDeptsToYonyou() {
        log.info("开始同步部门至用友系统");
        try {
            // 获取本地部门列表
            List<MultiDimensionDeptDto> localDeptList = getLocalDeptList();
            log.info("获取到本地部门数量：{}", localDeptList.size());
            // 存储同步结果
            List<DeptSaveResponse> syncResults = new ArrayList<>();
            
            // 遍历本地部门列表并同步到用友
            for (MultiDimensionDeptDto dept : localDeptList) {
                try {
                    // 构造部门保存请求
                    DeptSaveRequest deptSaveRequest = buildDeptSaveRequest(dept);
                    
                    // 调用部门保存接口
                    DeptSaveResponse response = saveDept(deptSaveRequest);
                    syncResults.add(response);

                } catch (Exception e) {
                    log.error("部门同步失败，部门名称：{}，错误信息：{}", dept.getName(), e.getMessage(), e);
                    // 继续处理下一个部门，不中断整个同步过程
                }
            }
            
            log.info("本地部门同步至用友系统完成，总共处理：{} 个部门", localDeptList.size());
            return syncResults;
        } catch (Exception e) {
            log.error("同步本地部门至用友系统异常", e);
            throw new RuntimeException("部门同步失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 构造部门保存请求对象
     *
     * @param dept 本地部门信息
     * @return {@link DeptSaveRequest} 部门保存请求对象
     */
    private DeptSaveRequest buildDeptSaveRequest(MultiDimensionDeptDto dept) {
        // 构造多语言名称
        DeptSaveRequest.MultiLangName multiLangName = DeptSaveRequest.MultiLangName.builder()
                .zh_CN(dept.getName())
                .build();
        
        // 构造部门数据
        DeptSaveRequest.DeptData deptData = DeptSaveRequest.DeptData.builder()
                .code(dept.getCode())
                .name(multiLangName)
                // 如果是更新操作，需要提供ID
                .id(null)
                // 设置上级部门ID
                .parent_code(null)
                // 1:启用
                .enable(1)
                // Update，如果部门不存在可以设置为"Insert"
                ._status("Insert")
                .build();
        
        // 构造部门保存请求
        return DeptSaveRequest.builder()
                .data(deptData)
                .build();
    }
}